nohup: 忽略输入
W0724 17:54:11.612668 3179954 site-packages/torch/distributed/run.py:793] 
W0724 17:54:11.612668 3179954 site-packages/torch/distributed/run.py:793] *****************************************
W0724 17:54:11.612668 3179954 site-packages/torch/distributed/run.py:793] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0724 17:54:11.612668 3179954 site-packages/torch/distributed/run.py:793] *****************************************
Process 3180023: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
3
script_name: vipt.py  config_name: coesot.yaml
Process 3180021: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
1
Process 3180020: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 3180022: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
0
2
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
New configuration is shown below.
MODEL configuration: {'PRETRAIN_FILE': './pretrained/OSTrack_coesot.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}


TRAIN configuration: {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.001, 'WEIGHT_DECAY': 5e-05, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 5, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}


DATA configuration: {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['COESOT'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['COESOT_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 40000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}


TEST configuration: {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 60}


sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
Learnable parameters are shown below.
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
module.backbone.voxel_extractor.sparse_encoder.sparse_conv1.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv1.bias
module.backbone.voxel_extractor.sparse_encoder.norm1.weight
module.backbone.voxel_extractor.sparse_encoder.norm1.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv2.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv2.bias
module.backbone.voxel_extractor.sparse_encoder.norm2.weight
module.backbone.voxel_extractor.sparse_encoder.norm2.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv3.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv3.bias
module.backbone.voxel_extractor.sparse_encoder.norm3.weight
module.backbone.voxel_extractor.sparse_encoder.norm3.bias
module.backbone.voxel_extractor.sparse_encoder.sparse_conv4.weight
module.backbone.voxel_extractor.sparse_encoder.sparse_conv4.bias
module.backbone.voxel_extractor.sparse_encoder.norm4.weight
module.backbone.voxel_extractor.sparse_encoder.norm4.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_pool.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_pool.bias
module.backbone.voxel_extractor.sparse_encoder.norm_pool.weight
module.backbone.voxel_extractor.sparse_encoder.norm_pool.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_conv.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_conv.bias
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_norm.weight
module.backbone.voxel_extractor.sparse_encoder.temporal_diff_norm.bias
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_conv.weight
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_conv.bias
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_norm.weight
module.backbone.voxel_extractor.sparse_encoder.spatial_grad_norm.bias
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_conv.weight
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_conv.bias
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_norm.weight
module.backbone.voxel_extractor.sparse_encoder.unified_fusion_norm.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.motion_saliency.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.motion_saliency.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.temporal_consistency.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.temporal_consistency.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.spatial_coherence.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.spatial_coherence.bias
module.backbone.voxel_extractor.spatiotemporal_correlation.correlation_fusion.weight
module.backbone.voxel_extractor.spatiotemporal_correlation.correlation_fusion.bias
module.backbone.voxel_extractor.adaptive_temporal_attention.0.weight
module.backbone.voxel_extractor.adaptive_temporal_attention.0.bias
module.backbone.voxel_extractor.adaptive_temporal_attention.2.weight
module.backbone.voxel_extractor.adaptive_temporal_attention.2.bias
module.backbone.voxel_injectors.0.cross_attention_filter.template_to_query.weight
module.backbone.voxel_injectors.0.cross_attention_filter.template_to_query.bias
module.backbone.voxel_injectors.0.cross_attention_filter.voxel_to_key.weight
module.backbone.voxel_injectors.0.cross_attention_filter.voxel_to_key.bias
module.backbone.voxel_injectors.0.cross_attention_filter.voxel_to_value.weight
module.backbone.voxel_injectors.0.cross_attention_filter.voxel_to_value.bias
module.backbone.voxel_injectors.0.cross_attention_filter.output_proj.weight
module.backbone.voxel_injectors.0.cross_attention_filter.output_proj.bias
module.backbone.voxel_injectors.0.cross_attention_filter.norm.weight
module.backbone.voxel_injectors.0.cross_attention_filter.norm.bias
module.backbone.voxel_injectors.0.feature_fusion.0.weight
module.backbone.voxel_injectors.0.feature_fusion.0.bias
module.backbone.voxel_injectors.0.feature_fusion.2.weight
module.backbone.voxel_injectors.0.feature_fusion.2.bias
module.backbone.voxel_injectors.0.adaptive_weight.0.weight
module.backbone.voxel_injectors.0.adaptive_weight.0.bias
module.backbone.voxel_injectors.0.adaptive_weight.2.weight
module.backbone.voxel_injectors.0.adaptive_weight.2.bias
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
No matching checkpoint file found
No matching checkpoint file found
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
Training crashed at epoch 1
Traceback for the error!
Traceback (most recent call last):
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/base_trainer.py", line 86, in train
    self.train_epoch()
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 205, in train_epoch
    self.cycle_dataset(loader)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 162, in cycle_dataset
    loss, stats = self.actor(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 41, in __call__
    out_dict = self.forward_pass(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 80, in forward_pass
    out_dict = self.net(template=template_list,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/ostrack_prompt.py", line 47, in forward
    x, aux_dict = self.backbone(z=template, x=search, z_voxel=template_voxel, x_voxel=search_voxel,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 460, in forward
    x, aux_dict = self.forward_features(z, x, z_voxel=z_voxel, x_voxel=x_voxel, ce_template_mask=ce_template_mask, ce_keep_rate=ce_keep_rate,)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 327, in forward_features
    processed_motion_features, motion_intensity = self.voxel_extractor(
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 246, in forward
    attention_weights = self.compute_adaptive_temporal_weights(dense_features, spatiotemporal_correlation)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 299, in compute_adaptive_temporal_weights
    attention_logits = self.adaptive_temporal_attention(enhanced_features)  # [B, 1, T, H, W]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/container.py", line 250, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 725, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 720, in _conv_forward
    return F.conv3d(
RuntimeError: Given groups=1, weight of size [32, 257, 3, 3, 3], expected input[64, 512, 4, 16, 16] to have 257 channels, but got 512 channels instead

Restarting training from last epoch ...
Finished training!
Training crashed at epoch 1
Traceback for the error!
Traceback (most recent call last):
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/base_trainer.py", line 86, in train
    self.train_epoch()
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 205, in train_epoch
    self.cycle_dataset(loader)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 162, in cycle_dataset
    loss, stats = self.actor(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 41, in __call__
    out_dict = self.forward_pass(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 80, in forward_pass
    out_dict = self.net(template=template_list,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/ostrack_prompt.py", line 47, in forward
    x, aux_dict = self.backbone(z=template, x=search, z_voxel=template_voxel, x_voxel=search_voxel,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 460, in forward
    x, aux_dict = self.forward_features(z, x, z_voxel=z_voxel, x_voxel=x_voxel, ce_template_mask=ce_template_mask, ce_keep_rate=ce_keep_rate,)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 327, in forward_features
    processed_motion_features, motion_intensity = self.voxel_extractor(
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 246, in forward
    attention_weights = self.compute_adaptive_temporal_weights(dense_features, spatiotemporal_correlation)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 299, in compute_adaptive_temporal_weights
    attention_logits = self.adaptive_temporal_attention(enhanced_features)  # [B, 1, T, H, W]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/container.py", line 250, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 725, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 720, in _conv_forward
    return F.conv3d(
RuntimeError: Given groups=1, weight of size [32, 257, 3, 3, 3], expected input[64, 512, 4, 16, 16] to have 257 channels, but got 512 channels instead

Restarting training from last epoch ...
Finished training!
Training crashed at epoch 1
Traceback for the error!
Traceback (most recent call last):
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/base_trainer.py", line 86, in train
    self.train_epoch()
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 205, in train_epoch
    self.cycle_dataset(loader)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 162, in cycle_dataset
    loss, stats = self.actor(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 41, in __call__
    out_dict = self.forward_pass(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 80, in forward_pass
    out_dict = self.net(template=template_list,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/ostrack_prompt.py", line 47, in forward
    x, aux_dict = self.backbone(z=template, x=search, z_voxel=template_voxel, x_voxel=search_voxel,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 460, in forward
    x, aux_dict = self.forward_features(z, x, z_voxel=z_voxel, x_voxel=x_voxel, ce_template_mask=ce_template_mask, ce_keep_rate=ce_keep_rate,)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 327, in forward_features
    processed_motion_features, motion_intensity = self.voxel_extractor(
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 246, in forward
    attention_weights = self.compute_adaptive_temporal_weights(dense_features, spatiotemporal_correlation)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 299, in compute_adaptive_temporal_weights
    attention_logits = self.adaptive_temporal_attention(enhanced_features)  # [B, 1, T, H, W]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/container.py", line 250, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 725, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 720, in _conv_forward
    return F.conv3d(
RuntimeError: Given groups=1, weight of size [32, 257, 3, 3, 3], expected input[64, 512, 4, 16, 16] to have 257 channels, but got 512 channels instead

Restarting training from last epoch ...
Finished training!
Training crashed at epoch 1
Traceback for the error!
Traceback (most recent call last):
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/base_trainer.py", line 86, in train
    self.train_epoch()
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 205, in train_epoch
    self.cycle_dataset(loader)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/trainers/ltr_trainer.py", line 162, in cycle_dataset
    loss, stats = self.actor(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 41, in __call__
    out_dict = self.forward_pass(data)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/train/actors/vipt.py", line 80, in forward_pass
    out_dict = self.net(template=template_list,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1643, in forward
    else self._run_ddp_forward(*inputs, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/parallel/distributed.py", line 1459, in _run_ddp_forward
    return self.module(*inputs, **kwargs)  # type: ignore[index]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/ostrack_prompt.py", line 47, in forward
    x, aux_dict = self.backbone(z=template, x=search, z_voxel=template_voxel, x_voxel=search_voxel,
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 460, in forward
    x, aux_dict = self.forward_features(z, x, z_voxel=z_voxel, x_voxel=x_voxel, ce_template_mask=ce_template_mask, ce_keep_rate=ce_keep_rate,)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/vipt/vit_ce_prompt.py", line 327, in forward_features
    processed_motion_features, motion_intensity = self.voxel_extractor(
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 246, in forward
    attention_weights = self.compute_adaptive_temporal_weights(dense_features, spatiotemporal_correlation)
  File "/home/<USER>/STU/workspaces/ruihui/ViPT/lib/train/../../lib/models/layers/voxel_motion_extractor.py", line 299, in compute_adaptive_temporal_weights
    attention_logits = self.adaptive_temporal_attention(enhanced_features)  # [B, 1, T, H, W]
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/container.py", line 250, in forward
    input = module(input)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 725, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/torch/nn/modules/conv.py", line 720, in _conv_forward
    return F.conv3d(
RuntimeError: Given groups=1, weight of size [32, 257, 3, 3, 3], expected input[64, 512, 4, 16, 16] to have 257 channels, but got 512 channels instead

Restarting training from last epoch ...
Finished training!
[rank0]:[W724 17:55:16.179321897 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())
args.config  coesot
