import math
import torch
import copy
import torch.nn.functional as F
from lib.models.vipt import build_viptrack
from lib.test.tracker.basetracker import BaseTracker
import torch
from lib.test.tracker.vis_utils import gen_visualization
from lib.test.utils.hann import hann2d
from lib.train.data.processing_utils import sample_target
# for debug
import cv2
import os
# import vot
from lib.test.tracker.data_utils import PreprocessorMM
from lib.utils.box_ops import clip_box
from lib.utils.ce_utils import generate_mask_cond
from lib.train.data.processing import prepare_enhanced_voxel_data, organize_voxel_grid


class ViPTTrack(BaseTracker):
    def __init__(self, params):
        super(ViPTTrack, self).__init__(params)
        network = build_viptrack(params.cfg, training=False)
        network.load_state_dict(torch.load(self.params.checkpoint, map_location='cpu', weights_only=False)['net'], strict=True)
        self.cfg = params.cfg
        self.network = network.cuda()
        self.network.eval()
        self.preprocessor = PreprocessorMM()
        self.state = None

        self.feat_sz = self.cfg.TEST.SEARCH_SIZE // self.cfg.MODEL.BACKBONE.STRIDE
        # motion constrain
        self.output_window = hann2d(torch.tensor([self.feat_sz, self.feat_sz]).long(), centered=True).cuda()

        # for debug
        if getattr(params, 'debug', None) is None:
            setattr(params, 'debug', 0)
        self.use_visdom = False #params.debug
        self.debug = params.debug
        self.frame_id = 0
        # for save boxes from all queries
        self.save_all_boxes = params.save_all_boxes

    def processing_voxel(self, voxel, crop_coor, init=False):
        if voxel is None:
            return None

        voxel = voxel.cuda()
        z = copy.deepcopy(voxel[:, 0])
        x, y = voxel[:, 1], voxel[:, 2]
        voxel[:, 0] = x
        voxel[:, 1] = y
        voxel[:, 2] = z
        # crop to select voxels; template crop and search crop into the four times region.  // 10 resize
        x1, x2 = crop_coor[0] / 10, crop_coor[1] / 10
        y1, y2 = crop_coor[2] / 10, crop_coor[3] / 10
        ### coor normalized to 0-1 becasue of box coor
        x_range, y_range = x2 - x1, y2 - y1
        t_min = z.min()
        t_max = z.max() + 1e-6  # 避免除零
        voxel[:, 0] = (voxel[:, 0]+0.5 - x1) / x_range
        voxel[:, 1] = (voxel[:, 1]+0.5 - y1) / y_range
        voxel[:, 2] = (voxel[:, 2]+0.5 - t_min) / (t_max - t_min)  # normalize to 0-1
        indices = (voxel[:, 0] >= 0) & (voxel[:, 0] <= 1) & \
                    (voxel[:, 1] >= 0) & (voxel[:, 1] <= 1)
        voxel = torch.index_select(voxel, dim=0, index=indices.nonzero().squeeze(1))
        
        coords = voxel[:, :3]
        polarity = voxel[:, 3:]
        s = 'template' if init else 'search'
        # 创建体素网格
        # voxel = prepare_enhanced_voxel_data(coords, polarity, s)
        voxel = organize_voxel_grid(coords, polarity)
        
        return voxel

    def initialize(self, image, voxel, info: dict):
        # forward the template once
        z_patch_arr, resize_factor, z_amask_arr, crop_coor  = sample_target(image, info['init_bbox'], self.params.template_factor, # type: ignore
                                                    output_sz=self.params.template_size)
        self.z_patch_arr = z_patch_arr
        template = self.preprocessor.process(z_patch_arr)
        with torch.no_grad():
            self.z_tensor = template

        self.box_mask_z = None
        if self.cfg.MODEL.BACKBONE.CE_LOC:
            template_bbox = self.transform_bbox_to_crop(info['init_bbox'], resize_factor,
                                                        template.device).squeeze(1)
            self.box_mask_z = generate_mask_cond(self.cfg, 1, template.device, template_bbox)

        # process the voxel
        voxel = self.processing_voxel(voxel, crop_coor, init=True)
        self.template_voxel = voxel

        # save states
        self.state = info['init_bbox']
        self.frame_id = 0
        if self.save_all_boxes:
            '''save all predicted boxes'''
            all_boxes_save = info['init_bbox'] * self.cfg.MODEL.NUM_OBJECT_QUERIES
            return {"all_boxes": all_boxes_save}

    def track(self, image, voxel, info: dict = None):
        H, W, _ = image.shape
        self.frame_id += 1
        x_patch_arr, resize_factor, x_amask_arr, crop_coor = sample_target(image, self.state, self.params.search_factor, # type: ignore
                                                                output_sz=self.params.search_size)  # (x1, y1, w, h)
        search = self.preprocessor.process(x_patch_arr)

        # process the voxel
        voxel = self.processing_voxel(voxel, crop_coor, init=False)

        with torch.no_grad():
            x_tensor = search
            # merge the template and the search
            # run the transformer
            out_dict = self.network.forward(
                template=self.z_tensor, search=x_tensor, template_voxel=self.template_voxel, search_voxel=voxel, ce_template_mask=self.box_mask_z)

        # add hann windows
        pred_score_map = out_dict['score_map']
        response = self.output_window * pred_score_map
        pred_boxes, best_score = self.network.box_head.cal_bbox(response, out_dict['size_map'], out_dict['offset_map'], return_score=True)
        max_score = best_score[0][0].item()
        pred_boxes = pred_boxes.view(-1, 4)
        # Baseline: Take the mean of all pred boxes as the final result
        pred_box = (pred_boxes.mean(
            dim=0) * self.params.search_size / resize_factor).tolist()  # (cx, cy, w, h) [0,1]
        # get the final box result
        self.state = clip_box(self.map_box_back(pred_box, resize_factor), H, W, margin=10)

        # for debug
        if self.debug == 1:
            x1, y1, w, h = self.state
            image_BGR = cv2.cvtColor(image[:,:,:3], cv2.COLOR_RGB2BGR)
            cv2.rectangle(image_BGR, (int(x1), int(y1)), (int(x1 + w), int(y1 + h)), color=(0, 0, 255), thickness=2)
            cv2.putText(image_BGR, 'max_score:' + str(round(max_score, 3)), (40, 40),
                            cv2.FONT_HERSHEY_SIMPLEX, 1,
                            (0, 255, 255), 2)
            cv2.imshow('debug_vis', image_BGR)
            cv2.waitKey(1)

        if self.save_all_boxes:
            '''save all predictions'''
            all_boxes = self.map_box_back_batch(pred_boxes * self.params.search_size / resize_factor, resize_factor)
            all_boxes_save = all_boxes.view(-1).tolist()  # (4N, )
            return {"target_bbox": self.state,
                    "all_boxes": all_boxes_save,
                    "best_score": max_score}
        else:
            return {"target_bbox": self.state,
                    "best_score": max_score}

    def map_box_back(self, pred_box: list, resize_factor: float):
        cx_prev, cy_prev = self.state[0] + 0.5 * self.state[2], self.state[1] + 0.5 * self.state[3]
        cx, cy, w, h = pred_box
        half_side = 0.5 * self.params.search_size / resize_factor
        cx_real = cx + (cx_prev - half_side)
        cy_real = cy + (cy_prev - half_side)
        return [cx_real - 0.5 * w, cy_real - 0.5 * h, w, h]

    def map_box_back_batch(self, pred_box: torch.Tensor, resize_factor: float):
        cx_prev, cy_prev = self.state[0] + 0.5 * self.state[2], self.state[1] + 0.5 * self.state[3]
        cx, cy, w, h = pred_box.unbind(-1) # (N,4) --> (N,)
        half_side = 0.5 * self.params.search_size / resize_factor
        cx_real = cx + (cx_prev - half_side)
        cy_real = cy + (cy_prev - half_side)
        return torch.stack([cx_real - 0.5 * w, cy_real - 0.5 * h, w, h], dim=-1)


def get_tracker_class():
    return ViPTTrack
